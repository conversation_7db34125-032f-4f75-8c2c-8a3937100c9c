{% extends 'base.html' %}

{% block title %}入库单管理{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 入库管理页面样式 - 保持系统风格统一 */
.compact-toolbar {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.filter-collapse {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.stock-in-number {
    font-weight: 600;
    color: #007bff;
    font-size: 15px;
    line-height: 1.2;
}

.stock-in-info {
    line-height: 1.3;
}

.stock-in-info .purchase-order-link {
    display: inline-block;
    margin-top: 2px;
    padding: 1px 4px;
    background-color: #f8f9fa;
    border-radius: 3px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.stock-in-info .purchase-order-link:hover {
    background-color: #e9ecef;
    text-decoration: none;
}

.btn-group-compact {
    display: flex;
    gap: 3px;
    flex-wrap: wrap;
}

.btn-xs {
    padding: 3px 8px;
    font-size: 12px;
    line-height: 1.3;
}

.table-compact {
    font-size: 15px;
}

.table-compact th {
    padding: 10px 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-weight: 600;
    font-size: 14px;
    color: white;
    border: none;
}

.table-compact td {
    padding: 10px 8px;
    vertical-align: middle;
}

.badge-sm {
    font-size: 11px;
    padding: 3px 8px;
}

.consumption-status {
    font-size: 12px;
    margin-top: 3px;
}

.consumption-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
}

.consumption-indicator.not-consumed { background-color: #28a745; }
.consumption-indicator.partially-consumed { background-color: #ffc107; }
.consumption-indicator.mostly-consumed { background-color: #fd7e14; }
.consumption-indicator.fully-consumed { background-color: #6c757d; }
.consumption-indicator.error { background-color: #dc3545; }

/* 响应式设计 */
@media (max-width: 768px) {
    .compact-toolbar {
        padding: 10px 15px;
    }

    .table-compact {
        font-size: 14px;
    }

    .table-compact th,
    .table-compact td {
        padding: 8px 6px;
    }
}
</style>

{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 精简的入库管理工具栏 -->
    <div class="compact-toolbar d-flex justify-content-between align-items-center mb-3">
        <!-- 左侧：页面标题 -->
        <div class="d-flex align-items-center">
            <h5 class="mb-0 mr-3"><i class="fas fa-truck-loading mr-2"></i>入库管理</h5>
        </div>

        <!-- 桌面端快捷操作 -->
        <div class="d-flex align-items-center desktop-only">
            <a href="{{ url_for('stock_in.create') }}" class="btn btn-primary btn-sm mr-2">
                <i class="fas fa-plus"></i> 创建入库单
            </a>
            <a href="{{ url_for('stock_in_wizard.wizard') }}" class="btn btn-success btn-sm mr-2">
                <i class="fas fa-magic"></i> 向导式入库
            </a>
            <a href="{{ url_for('inspection.index') }}" class="btn btn-outline-secondary btn-sm mr-2">
                <i class="fas fa-clipboard-check"></i> 入库检查
            </a>
            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-primary btn-sm mr-2">
                <i class="fas fa-clipboard-list"></i> 消耗计划
            </a>
            <button type="button" class="btn btn-outline-secondary btn-sm" data-toggle="collapse" data-target="#filterForm">
                <i class="fas fa-filter"></i> 筛选
            </button>
        </div>

        <!-- 移动端快捷操作 -->
        <div class="mobile-only action-buttons">
            <a href="{{ url_for('stock_in.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 创建入库单
            </a>
            <a href="{{ url_for('stock_in_wizard.wizard') }}" class="btn btn-success">
                <i class="fas fa-magic"></i> 向导式入库
            </a>
            <a href="{{ url_for('inspection.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-clipboard-check"></i> 入库检查
            </a>
            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-clipboard-list"></i> 消耗计划
            </a>
            <button type="button" class="btn btn-outline-secondary" data-toggle="collapse" data-target="#filterForm">
                <i class="fas fa-filter"></i> 筛选
            </button>
        </div>
    </div>

    <!-- 可折叠的筛选区域 -->
    <div class="collapse mb-3" id="filterForm">
        <div class="filter-collapse">
            <form method="get" action="{{ url_for('stock_in.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <select name="status" class="form-control form-control-sm">
                            <option value="">全部状态</option>
                            <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                            <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                            <option value="已入库" {% if status == '已入库' %}selected{% endif %}>已入库</option>
                            <option value="已取消" {% if status == '已取消' %}selected{% endif %}>已取消</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="start_date" class="form-control form-control-sm" value="{{ start_date }}" placeholder="开始日期">
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="end_date" class="form-control form-control-sm" value="{{ end_date }}" placeholder="结束日期">
                    </div>
                    <div class="col-md-2">
                        <input type="text" name="purchase_order" class="form-control form-control-sm" value="{{ purchase_order }}" placeholder="采购订单号">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary btn-sm mr-1">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('stock_in.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i> 清除
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 桌面端表格 -->
    <div class="table-responsive desktop-only">
        <table class="table table-compact table-hover table-bordered">
            <thead>
                <tr>
                    <th style="width: 15%;">📋 入库单号</th>
                    <th style="width: 10%;">仓库</th>
                    <th style="width: 10%;">入库日期</th>
                    <th style="width: 12%;">供应商</th>
                    <th style="width: 8%;">操作人</th>
                    <th style="width: 8%;">状态</th>
                    <th style="width: 12%;">消耗状态</th>
                    <th style="width: 15%;">操作</th>
                </tr>
            </thead>
                        <tbody>
                            {% for stock_in in stock_ins %}
                            <tr>
                                <td>
                                    <div class="stock-in-info">
                                        <div class="stock-in-number">{{ stock_in.stock_in_number }}</div>
                                        <small class="text-muted">{{ stock_in.stock_in_type }}</small>
                                        {% if stock_in.purchase_order %}
                                        <br>
                                        <a href="{{ url_for('purchase_order.view', id=stock_in.purchase_order.id) }}"
                                           class="purchase-order-link text-primary" title="查看采购订单">
                                            <small><i class="fas fa-shopping-cart"></i> {{ stock_in.purchase_order.order_number }}</small>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <small>{{ stock_in.warehouse.name }}</small>
                                </td>
                                <td>
                                    <small>{{ stock_in.stock_in_date|format_datetime('%m-%d') }}</small>
                                </td>
                                <td>
                                    <small>{{ stock_in.supplier.name if stock_in.supplier else '-' }}</small>
                                </td>
                                <td>
                                    <small>{{ stock_in.operator.real_name or stock_in.operator.username }}</small>
                                </td>
                                <td>
                                    {% if stock_in.status == '待审核' %}
                                    <span class="badge badge-warning badge-sm">待审核</span>
                                    {% elif stock_in.status == '已审核' %}
                                    <span class="badge badge-info badge-sm">已审核</span>
                                    {% elif stock_in.status == '已入库' %}
                                    <span class="badge badge-success badge-sm">已入库</span>
                                    {% elif stock_in.status == '已取消' %}
                                    <span class="badge badge-danger badge-sm">已取消</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if stock_in.status == '已入库' and stock_in.id in stock_in_consumption_status %}
                                        {% set consumption_info = stock_in_consumption_status[stock_in.id] %}
                                        <div class="consumption-status">
                                            <span class="consumption-indicator {{ consumption_info.status }}"></span>
                                            {% if consumption_info.status == 'not_consumed' %}
                                                <span class="text-success">未消耗</span>
                                            {% elif consumption_info.status == 'partially_consumed' %}
                                                <span class="text-warning">部分消耗</span>
                                            {% elif consumption_info.status == 'mostly_consumed' %}
                                                <span class="text-warning">大部分消耗</span>
                                            {% elif consumption_info.status == 'fully_consumed' %}
                                                <span class="text-muted">完全消耗</span>
                                            {% else %}
                                                <span class="text-danger">计算错误</span>
                                            {% endif %}
                                            <br><small class="text-muted">{{ consumption_info.consumption_rate }}%</small>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-compact">
                                        <a href="{{ url_for('stock_in.view', id=stock_in.id) }}"
                                           class="btn btn-xs btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if stock_in.status == '待审核' %}
                                        <a href="{{ url_for('stock_in.edit', id=stock_in.id) }}"
                                           class="btn btn-xs btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-xs btn-outline-danger"
                                                data-action="critical-confirm" data-confirm-message="确定要删除入库单吗？" data-original-onclick="deleteStockIn({{ stock_in.id }}, '{{ stock_in.stock_in_number }}')" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-xs btn-outline-secondary"
                                                onclick="printStockIn({{ stock_in.id }})" style="cursor: pointer;" title="打印">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="fas fa-inbox text-muted"></i>
                                    <br><small class="text-muted">暂无入库单数据</small>
                                </td>
                            </tr>
                            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 移动端卡片视图 -->
    <div class="mobile-only">
        {% for stock_in in stock_ins %}
        <div class="card mb-3 border-left-{% if stock_in.status == '待审核' %}warning{% elif stock_in.status == '已审核' %}info{% elif stock_in.status == '已入库' %}success{% else %}secondary{% endif %}">
            <div class="card-body py-2">
                <div class="row">
                    <div class="col-8">
                        <div class="stock-in-info">
                            <h6 class="mb-1 stock-in-number">{{ stock_in.stock_in_number }}</h6>
                            <small class="text-muted">{{ stock_in.warehouse.name }} | {{ stock_in.stock_in_type }}</small>
                            {% if stock_in.purchase_order %}
                            <br>
                            <a href="{{ url_for('purchase_order.view', id=stock_in.purchase_order.id) }}"
                               class="purchase-order-link text-primary">
                                <small><i class="fas fa-shopping-cart"></i> {{ stock_in.purchase_order.order_number }}</small>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-4 text-right">
                        {% if stock_in.status == '待审核' %}
                        <span class="badge badge-warning">{{ stock_in.status }}</span>
                        {% elif stock_in.status == '已审核' %}
                        <span class="badge badge-info">{{ stock_in.status }}</span>
                        {% elif stock_in.status == '已入库' %}
                        <span class="badge badge-success">{{ stock_in.status }}</span>
                        {% else %}
                        <span class="badge badge-secondary">{{ stock_in.status }}</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">入库日期</small>
                        <div class="small">{{ stock_in.stock_in_date|format_datetime('%m-%d') if stock_in.stock_in_date else '-' }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">操作人</small>
                        <div class="small">{{ stock_in.operator.real_name or stock_in.operator.username if stock_in.operator else '-' }}</div>
                    </div>
                </div>

                {% if stock_in.supplier %}
                <div class="row mt-1">
                    <div class="col-12">
                        <small class="text-muted">供应商</small>
                        <div class="small">{{ stock_in.supplier.name }}</div>
                    </div>
                </div>
                {% endif %}

                <div class="row mt-2">
                    <div class="col-12">
                        <div class="btn-group btn-group-sm w-100" role="group">
                            <a href="{{ url_for('stock_in.view', id=stock_in.id) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('stock_in.edit', id=stock_in.id) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-outline-info" onclick="printStockIn({{ stock_in.id }})">
                                <i class="fas fa-print"></i>
                            </button>
                            {% if stock_in.status == '待审核' %}
                            <button type="button" class="btn btn-outline-danger" data-onclick="deleteStockIn({{ stock_in.id }}, '{{ stock_in.stock_in_number }}')">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5>暂无入库单数据</h5>
            <p class="text-muted">您可以创建新的入库单或调整筛选条件</p>
        </div>
        {% endfor %}
    </div>

    <!-- 精简分页 -->
    {% if pagination.pages > 1 %}
    <div class="d-flex justify-content-center mt-3">
        <ul class="pagination pagination-sm">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('stock_in.index', page=pagination.prev_num, status=status, start_date=start_date, end_date=end_date, purchase_order=purchase_order) }}">
                    «
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">«</span>
            </li>
            {% endif %}

            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('stock_in.index', page=page, status=status, start_date=start_date, end_date=end_date, purchase_order=purchase_order) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('stock_in.index', page=pagination.next_num, status=status, start_date=start_date, end_date=end_date, purchase_order=purchase_order) }}">
                    »
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">»</span>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    function printStockIn(stockInId) {
        window.open("{{ url_for('stock_in.print_stock_in', id=0) }}".replace('0', stockInId), '_blank');
    }

    function deleteStockIn(stockInId, stockInNumber) {
        if (confirm(`确定要删除入库单 ${stockInNumber} 吗？\n\n注意：只有待审核状态且未实际入库的入库单可以删除。\n此操作不可恢复！`)) {
            $.ajax({
                url: `/stock-in/${stockInId}/delete`,
                type: 'POST',
                success: function(data) {
                    if (data.success) {
                        alert('入库单删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败：' + data.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('删除入库单失败:', error);
                    alert('删除失败，请稍后重试');
                }
            });
        }
    }
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
{% endblock %}

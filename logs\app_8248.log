2025-06-14 21:54:18,327 INFO: 应用启动 - PID: 8248 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-14 21:55:40,914 ERROR: 查看入库食材详情失败: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'menu_plans' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            SELECT
                cp.id, cp.consumption_date, cp.meal_type, cp.diners_count,
                a.name as area_name, a.id as area_id
            FROM
                stock_out_items soi
            JOIN
                stock_outs so ON soi.stock_out_id = so.id
            JOIN
                consumption_plans cp ON so.consumption_plan_id = cp.id
            JOIN
                menu_plans mp ON cp.menu_plan_id = mp.id
            JOIN
                administrative_areas a ON mp.area_id = a.id
            WHERE
                soi.batch_number = ?
            ORDER BY
                cp.consumption_date DESC
        ]
[parameters: ('B2025061489324c',)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\StudentsCMSSP\app\routes\stock_in_detail.py:124]

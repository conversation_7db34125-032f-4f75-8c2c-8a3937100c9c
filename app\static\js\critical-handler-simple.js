/**
 * 增强的关键事件处理器
 * 处理所有类型的删除确认和表单验证
 */


    // 安全执行函数（替代eval）
    function safeExecute(code) {
        if (!code) return;
        code = code.trim();
        
        // 处理confirm调用
        if (code.includes('confirm(')) {
            const confirmMatch = code.match(/confirm\s*\(\s*['"](.*?)['"]\s*\)/);
            if (confirmMatch) {
                return confirm(confirmMatch[1]);
            }
        }
        
        console.warn('不支持的代码执行:', code);
        return false;
    }
    
    // 安全函数创建器（替代Function构造器）
    function createSafeFunction(code) {
        return function() {
            return safeExecute(code);
        };
    }

    
    // 安全执行函数（替代eval）
    function safeExecute(code) {
        if (!code) return;
        code = code.trim();

        console.log('🔍 critical-handler-simple 尝试执行代码:', code);

        // 处理confirm调用 - 改进的正则表达式
        if (code.includes('confirm(')) {
            console.log('🔍 检测到confirm调用:', code);

            // 多种confirm调用格式的正则表达式
            const confirmPatterns = [
                /confirm\s*\(\s*['"](.*?)['"]\s*\)/,  // confirm('message')
                /confirm\s*\(\s*"([^"]*)"\s*\)/,      // confirm("message")
                /confirm\s*\(\s*'([^']*)'\s*\)/,      // confirm('message')
                /confirm\s*\(\s*([^)]+)\s*\)/         // confirm(variable)
            ];

            for (let pattern of confirmPatterns) {
                const match = code.match(pattern);
                if (match) {
                    const message = match[1] || '确定要执行此操作吗？';
                    console.log('✅ 执行confirm，消息:', message);
                    return confirm(message);
                }
            }

            // 如果没有匹配到参数，使用默认消息
            console.log('⚠️ confirm调用格式不标准，使用默认消息');
            return confirm('确定要执行此操作吗？');
        }

        // 处理函数调用（如 deleteDocument(123)）
        const functionCallPattern = /^([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*(.*?)\s*\)$/;
        const functionMatch = code.match(functionCallPattern);

        if (functionMatch) {
            const functionName = functionMatch[1];
            const argsString = functionMatch[2];

            console.log('🔍 检测到函数调用:', functionName, '参数:', argsString);

            // 检查函数是否存在于全局作用域
            if (typeof window[functionName] === 'function') {
                try {
                    // 解析参数
                    let args = [];
                    if (argsString.trim()) {
                        // 简单的参数解析（支持数字、字符串）
                        const argParts = argsString.split(',').map(arg => arg.trim());
                        args = argParts.map(arg => {
                            // 数字参数
                            if (/^\d+$/.test(arg)) {
                                return parseInt(arg);
                            }
                            // 字符串参数（去掉引号）
                            if (/^['"].*['"]$/.test(arg)) {
                                return arg.slice(1, -1);
                            }
                            // 其他情况返回原始字符串
                            return arg;
                        });
                    }

                    console.log('✅ 执行函数:', functionName, '参数:', args);
                    return window[functionName].apply(null, args);
                } catch (error) {
                    console.error('❌ 函数执行失败:', error);
                    throw error;
                }
            } else {
                console.warn('❌ 函数不存在:', functionName);
                throw new Error(`函数 ${functionName} 不存在`);
            }
        }

        console.warn('❌ 不支持的代码执行:', code);
        return false;
    }
    
    // 安全函数创建器（替代Function构造器）
    function createSafeFunction(code) {
        return function() {
            return safeExecute(code);
        };
    }

    document.addEventListener('DOMContentLoaded', function() {

    // 处理所有关键确认操作
    document.querySelectorAll('[data-action="critical-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            const originalOnclick = this.getAttribute('data-original-onclick');
            const deleteCode = this.getAttribute('data-delete-code');
            const confirmMessage = this.getAttribute('data-confirm-message') || '确定要执行此操作吗？';

            // 优先使用 data-delete-code 属性（新的标准）
            if (deleteCode) {
                console.log('🔍 使用 data-delete-code:', deleteCode);

                if (confirm(confirmMessage)) {
                    try {
                        executeOriginalCode(deleteCode);
                    } catch (error) {
                        console.error('删除操作执行失败:', error);
                        alert('删除失败，请重试');
                    }
                }
            }
            // 检查 originalOnclick 是否存在且包含 confirm
            else if (originalOnclick && originalOnclick.includes('confirm(')) {
                // 提取确认消息
                const confirmMatch = originalOnclick.match(/confirm\s*\(\s*['"](.*?)['"]\s*\)/);
                const extractedMessage = confirmMatch ? confirmMatch[1] : '确定要执行此操作吗？';

                if (confirm(extractedMessage)) {
                    try {
                        // 执行原有逻辑
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                        alert('操作失败，请重试');
                    }
                }
            } else if (originalOnclick) {
                // 对于没有确认的删除操作，添加确认
                if (originalOnclick.toLowerCase().includes('delete') ||
                    originalOnclick.toLowerCase().includes('remove')) {

                    if (confirm('确定要删除吗？')) {
                        try {
                            executeOriginalCode(originalOnclick);
                        } catch (error) {
                            console.error('删除失败:', error);
                            alert('删除失败，请重试');
                        }
                    }
                } else {
                    // 其他操作直接执行
                    try {
                        executeOriginalCode(originalOnclick);
                    } catch (error) {
                        console.error('执行失败:', error);
                    }
                }
            } else {
                // 检查是否是链接元素，如果是，使用href作为默认行为
                if (this.tagName.toLowerCase() === 'a' && this.href) {
                    console.log('🔗 检测到链接元素，使用href作为默认行为:', this.href);
                    if (confirm(confirmMessage)) {
                        window.location.href = this.href;
                    }
                } else {
                    // 两个属性都为空的情况，记录警告
                    console.warn('按钮缺少 data-original-onclick 或 data-delete-code 属性:', this);
                }
            }
        });
    });

    // 处理关键表单验证
    document.querySelectorAll('[data-validation="critical"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const originalOnsubmit = this.getAttribute('data-original-onsubmit');

            try {
                // 执行原有验证逻辑
                const result = executeOriginalCode(originalOnsubmit);

                // 如果返回 false，阻止提交
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败，已阻止提交');
                }
            } catch (error) {
                console.error('表单验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });

    // 处理之前的删除确认（兼容性）
    document.querySelectorAll('[data-action="delete-confirm"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            const functionCode = this.getAttribute('data-function');
            const confirmMessage = '确定要执行此操作吗？';

            if (confirm(confirmMessage)) {
                try {
                    executeOriginalCode(functionCode);
                } catch (error) {
                    console.error('执行失败:', error);
                    alert('操作失败，请重试');
                }
            }
        });
    });

    // 处理之前的表单验证（兼容性）
    document.querySelectorAll('[data-validation="true"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const validator = this.getAttribute('data-validator');

            try {
                const result = executeOriginalCode(validator);
                if (result === false) {
                    e.preventDefault();
                    console.log('表单验证失败');
                }
            } catch (error) {
                console.error('验证失败:', error);
                e.preventDefault();
                alert('表单验证失败，请检查输入');
            }
        });
    });

    // 安全执行原有代码的函数
    function executeOriginalCode(code) {
        if (!code) return;

        try {
            // 清理代码
            code = code.trim();

            // 如果是 return 语句，提取返回值
            if (code.startsWith('return ')) {
                code = code.substring(7);
                return safeExecute(code);
            } else {
                // 直接执行
                safeExecute(code);
                return true;
            }
        } catch (error) {
            console.error('代码执行失败:', error);
            throw error;
        }
    }

    console.log('✅ 增强的关键事件处理器已加载');
});